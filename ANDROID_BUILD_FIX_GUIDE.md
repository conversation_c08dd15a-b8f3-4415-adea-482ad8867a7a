# 🔧 دليل إصلاح مشكلة Android Build

## 🎯 المشكلة:
```
e: file:///android/app/build.gradle.kts:28:30: Expecting an element
e: file:///android/app/build.gradle.kts:28:9: Function invocation 'minSdkVersion(...)' expected
```

## 🔍 السبب الجذري:
ملف `build.gradle.kts` يحتوي على صيغة Groovy القديمة بدلاً من Kotlin DSL الصحيحة.

## 🛠️ الحل الشامل:

### الخطوة 1: استبدال android/app/build.gradle.kts
```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.wardlytec.app"
    compileSdk = 34

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.wardlytec.app"
        minSdk = 21
        targetSdk = 34
        versionCode = 1
        versionName = "1.0.0"
        multiDexEnabled = true
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation("androidx.multidx:multdx:2.0.1")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
```

### الخطوة 2: استبدال android/build.gradle
```groovy
buildscript {
    ext.kotlin_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
```

### الخطوة 3: استبدال android/settings.gradle
```groovy
pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.1.0" apply false
    id "org.jetbrains.kotlin.android" version "1.9.10" apply false
}

include ":app"
```

### الخطوة 4: تحديث android/gradle.properties
```properties
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=256m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.configureondemand=false

# Flutter build settings
flutter.compileSdkVersion=34
flutter.targetSdkVersion=34
flutter.minSdkVersion=21
flutter.versionCode=1
flutter.versionName=1.0.0
```

## 🚀 خطوات التطبيق:

### 1. نسخ احتياطي:
```bash
# انسخ مجلد android كاملاً كنسخة احتياطية
cp -r android android_backup
```

### 2. استبدال الملفات:
- احذف الملفات القديمة
- انسخ المحتوى الجديد من الأعلى

### 3. تنظيف وإعادة البناء:
```bash
flutter clean
flutter pub get
flutter build apk --release
```

## ✅ التحقق من النجاح:
يجب أن ترى:
```
✓ Built build/app/outputs/flutter-apk/app-release.apk (XX.X MB).
```

## 🔧 استكشاف الأخطاء:

### خطأ: "SDK not found"
```bash
# تأكد من وجود Android SDK
flutter doctor
```

### خطأ: "Gradle version"
```bash
# تحديث Gradle Wrapper
cd android
./gradlew wrapper --gradle-version 8.1
```

### خطأ: "Kotlin version"
```bash
# تأكد من تطابق إصدار Kotlin في جميع الملفات
```

## 📋 قائمة التحقق النهائية:
- [ ] استبدال android/app/build.gradle.kts
- [ ] استبدال android/build.gradle  
- [ ] استبدال android/settings.gradle
- [ ] تحديث android/gradle.properties
- [ ] تشغيل flutter clean
- [ ] تشغيل flutter pub get
- [ ] تشغيل flutter build apk --release

## 🎉 النتيجة المتوقعة:
بناء ناجح للتطبيق بدون أخطاء Gradle!
