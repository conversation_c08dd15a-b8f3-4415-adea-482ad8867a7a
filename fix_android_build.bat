@echo off
echo 🔧 بدء إصلاح مشكلة Android Build...
echo.

echo 📋 الخطوة 1: إنشاء نسخة احتياطية...
if exist android_backup (
    rmdir /s /q android_backup
)
xcopy android android_backup\ /e /i /h /y
echo ✅ تم إنشاء النسخة الاحتياطية في android_backup

echo.
echo 📝 الخطوة 2: تحديث ملفات Gradle...

echo 🔄 تحديث android/app/build.gradle.kts...
copy android_build_gradle_kts_fixed.txt android\app\build.gradle.kts /y

echo 🔄 تحديث android/build.gradle...
copy android_build_gradle_root_fixed.txt android\build.gradle /y

echo 🔄 تحديث android/settings.gradle...
copy android_settings_gradle_fixed.txt android\settings.gradle /y

echo 🔄 تحديث android/gradle.properties...
copy android_gradle_properties_fixed.txt android\gradle.properties /y

echo ✅ تم تحديث جميع ملفات Gradle

echo.
echo 🧹 الخطوة 3: تنظيف المشروع...
call flutter clean
echo ✅ تم تنظيف المشروع

echo.
echo 📦 الخطوة 4: تحديث التبعيات...
call flutter pub get
echo ✅ تم تحديث التبعيات

echo.
echo 🏗️ الخطوة 5: بناء التطبيق...
call flutter build apk --release

echo.
if %ERRORLEVEL% == 0 (
    echo 🎉 تم إصلاح المشكلة بنجاح!
    echo 📱 ملف APK جاهز في: build\app\outputs\flutter-apk\app-release.apk
) else (
    echo ❌ فشل في البناء. راجع الأخطاء أعلاه.
    echo 💡 يمكنك استعادة النسخة الاحتياطية من مجلد android_backup
)

echo.
echo 📋 للمراجعة اليدوية، راجع ملف: ANDROID_BUILD_FIX_GUIDE.md
pause
