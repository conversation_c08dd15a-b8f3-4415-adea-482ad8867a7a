#!/bin/bash

echo "🔧 بدء إصلاح مشكلة Android Build..."
echo

echo "📋 الخطوة 1: إنشاء نسخة احتياطية..."
if [ -d "android_backup" ]; then
    rm -rf android_backup
fi
cp -r android android_backup
echo "✅ تم إنشاء النسخة الاحتياطية في android_backup"

echo
echo "📝 الخطوة 2: تحديث ملفات Gradle..."

echo "🔄 تحديث android/app/build.gradle.kts..."
cp android_build_gradle_kts_fixed.txt android/app/build.gradle.kts

echo "🔄 تحديث android/build.gradle..."
cp android_build_gradle_root_fixed.txt android/build.gradle

echo "🔄 تحديث android/settings.gradle..."
cp android_settings_gradle_fixed.txt android/settings.gradle

echo "🔄 تحديث android/gradle.properties..."
cp android_gradle_properties_fixed.txt android/gradle.properties

echo "✅ تم تحديث جميع ملفات Gradle"

echo
echo "🧹 الخطوة 3: تنظيف المشروع..."
flutter clean
echo "✅ تم تنظيف المشروع"

echo
echo "📦 الخطوة 4: تحديث التبعيات..."
flutter pub get
echo "✅ تم تحديث التبعيات"

echo
echo "🏗️ الخطوة 5: بناء التطبيق..."
flutter build apk --release

if [ $? -eq 0 ]; then
    echo
    echo "🎉 تم إصلاح المشكلة بنجاح!"
    echo "📱 ملف APK جاهز في: build/app/outputs/flutter-apk/app-release.apk"
else
    echo
    echo "❌ فشل في البناء. راجع الأخطاء أعلاه."
    echo "💡 يمكنك استعادة النسخة الاحتياطية من مجلد android_backup"
fi

echo
echo "📋 للمراجعة اليدوية، راجع ملف: ANDROID_BUILD_FIX_GUIDE.md"
